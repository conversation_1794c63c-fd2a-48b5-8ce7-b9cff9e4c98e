import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  ElementRef,
  EventEmitter,
  Injector,
  Input,
  OnChanges,
  OnInit,
  Output,
  Renderer2,
  SimpleChanges,
  booleanAttribute,
  forwardRef,
  numberAttribute,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NG_VALUE_ACCESSOR } from '@angular/forms';
import { FinControlValueAccessor } from '@fincloud/utils/control-value-accessor';
import {
  BehaviorSubject,
  combineLatest,
  filter,
  finalize,
  interval,
  map,
  startWith,
  switchMap,
  take,
  tap,
} from 'rxjs';

/**
 * Reusable AI suggestion component that provides animated typewriter overlay functionality
 * for form controls. Shows AI suggestions with character-by-character typewriter effect on value changes.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-ai-suggestion--docs Storybook Reference}
 */
@Component({
  selector: 'fin-ai-suggestion',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './ai-suggestion.component.html',
  styleUrl: './ai-suggestion.component.scss',
  host: {
    class: 'fin-ai-suggestion',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => FinAiSuggestionComponent),
      multi: true,
    },
  ],
})
export class FinAiSuggestionComponent
  extends FinControlValueAccessor
  implements OnInit, OnChanges
{
  /**
   * Enables AI suggestion animations. When true, suggestions appear on input changes with typewriter effect.
   * Animation only triggers when BOTH `aiEnabled` is true AND input value changes.
   * @default false
   */
  @Input({ transform: booleanAttribute }) aiEnabled = false;

  /**
   * Speed of the typewriter effect in milliseconds per character.
   * Lower values make the typewriter effect faster.
   * @default 100
   */
  @Input({ transform: numberAttribute }) typewriterSpeed = 100;

  /**
   * Emitted when the AI suggestion typewriter animation completes.
   * Only emits when the typewriter animation completes naturally, not when aiEnabled is disabled.
   */
  @Output() aiSuggestionReady = new EventEmitter<void>();

  /** Internal BehaviorSubject to manage aiEnabled state reactively */
  private aiEnabledSubject$$ = new BehaviorSubject<boolean>(false);

  /** Internal BehaviorSubject to manage typewriter text progression and visibility */
  private typewriterText$$ = new BehaviorSubject<string>('');

  /** Tracks `aiEnabled` input state for reactive state management. Combined with valueChanges to trigger animations. */
  protected aiEnabled$ = this.aiEnabledSubject$$.asObservable();

  /**
   * Observable that emits the progressively revealed typewriter text.
   * Template subscribes to this to display the character-by-character animation.
   * Emits empty string when no animation should be visible, eliminating need for separate visibility logic.
   */
  protected typewriterText$ = this.typewriterText$$.asObservable();

  constructor(
    injector: Injector,
    private destroyRef: DestroyRef,
    private elementRef: ElementRef,
    private renderer: Renderer2,
  ) {
    super(injector);
  }

  ngOnInit(): void {
    // Initialize aiEnabled state
    this.aiEnabledSubject$$.next(this.aiEnabled);

    // Initialize typewriter text as empty
    this.typewriterText$$.next('');

    // Set up AI suggestion animation logic
    this.setupAiSuggestionAnimation();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Handle aiEnabled changes
    if (changes['aiEnabled']) {
      const newValue = changes['aiEnabled'].currentValue;
      this.aiEnabledSubject$$.next(newValue);

      // Clear typewriter text immediately when aiEnabled becomes false
      if (!newValue) {
        this.typewriterText$$.next('');
        this.renderer.removeClass(
          this.elementRef.nativeElement,
          'fin-ai-suggestion-active',
        );
      }
    }
    // Note: typewriterSpeed changes will be picked up automatically in the next animation cycle
  }

  /**
   * Sets up the AI suggestion typewriter animation logic that triggers when both
   * aiEnabled is true AND the form control value changes
   */
  private setupAiSuggestionAnimation(): void {
    // Combine aiEnabled state with form control value changes
    combineLatest([
      this.aiEnabledSubject$$,
      this.control.valueChanges.pipe(startWith(this.control.value)),
    ])
      .pipe(
        // Only proceed when aiEnabled is true and we have a value change
        filter(([aiEnabled]: [boolean, string | null]) => aiEnabled === true),
        // Switch to typewriter animation for each new value
        switchMap(([, value]: [boolean, string | null]) => {
          // Add CSS class for styling when animation starts
          this.renderer.addClass(
            this.elementRef.nativeElement,
            'fin-ai-suggestion-active',
          );

          // Handle edge cases: empty, null, or undefined values
          const textToType = value?.toString() || '';

          if (textToType.length === 0) {
            // For empty values, emit empty string and complete immediately
            this.typewriterText$$.next('');
            return interval(1).pipe(take(1)); // Complete immediately with a single emission
          }

          // Create typewriter effect using interval and map
          return interval(this.typewriterSpeed).pipe(
            take(textToType.length),
            map((index: number) => textToType.substring(0, index + 1)),
            tap((partialText: string) =>
              this.typewriterText$$.next(partialText),
            ),
            // Add finalize to execute completion logic only when this inner observable completes
            finalize(() => {
              // Clear the text and remove CSS class when animation completes
              this.typewriterText$$.next('');
              this.renderer.removeClass(
                this.elementRef.nativeElement,
                'fin-ai-suggestion-active',
              );
              this.aiSuggestionReady.emit();
            }),
          );
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
