# ui-ai-suggestion

This library provides a reusable AI suggestion component for Angular applications with typewriter effect.

## Features

- Reactive AI suggestion typewriter animations
- Character-by-character text reveal effect
- Configurable typewriter speed (default: 100ms per character)
- Form control integration
- Proper cleanup and memory management
- Event emission on animation completion
- Handles edge cases (empty, null, undefined values)

## Usage

```typescript
import { FinAiSuggestionComponent } from '@fincloud/ui/ai-suggestion';

@Component({
  template: ` <fin-ai-suggestion [aiEnabled]="true" [typewriterSpeed]="150" [formControl]="formControl" (aiSuggestionReady)="onSuggestionReady()"> </fin-ai-suggestion> `,
})
export class MyComponent {
  formControl = new FormControl('');

  onSuggestionReady() {
    console.log('AI suggestion typewriter animation completed');
  }
}
```

## Running unit tests

Run `nx test ui-ai-suggestion` to execute the unit tests.
