# @fincloud/ui/input

A comprehensive Angular input component library providing form field components with advanced features like input masking, progress indicators, character counting, and AI suggestions.

## Components

### FinInputComponent (`fin-input`)

The main input component that provides a feature-rich form field with support for various input types, validation, and enhanced user experience features.

**Key Features:**

- Multiple input types: text, password, number, currency, percentage, integer, months, decimal
- Input masking for formatted data entry (currency, percentage, decimal, etc.)
- Password visibility toggle
- AI suggestion animations
- Progress bar integration
- Character limit enforcement
- Readonly mode support
- Validation message display
- Prefix and suffix content projection

**Basic Usage:**

```typescript
import { FinInputModule } from '@fincloud/ui/input';

// In template
<fin-input
  label="Enter amount"
  type="currency"
  [formControl]="amountControl">
</fin-input>
```

### FinInputCountdownComponent (`fin-input-countdown`)

A character countdown component that displays remaining characters when used with inputs that have a maximum length.

**Key Features:**

- Real-time character count display
- Automatic integration with parent input's maxLength
- Visual feedback for character limits

**Basic Usage:**

```typescript
// Used as suffix in fin-input
<fin-input
  label="Description"
  [formControl]="descriptionControl"
  maxLength="100">
  <fin-input-countdown finInputSuffix></fin-input-countdown>
</fin-input>
```

### FinInputProgressComponent (`fin-input-progress`)

A progress indicator component typically used with password fields to show password strength or completion status.

**Key Features:**

- Configurable progress percentage
- Success threshold indicator
- Visual progress feedback

**Basic Usage:**

```typescript
// Used with password input
<fin-input
  label="Password"
  type="password"
  showProgress
  [formControl]="passwordControl">
  <ng-container finInputProgress>
    <fin-input-progress
      [progress]="passwordStrength"
      [successPercentage]="75">
    </fin-input-progress>
  </ng-container>
</fin-input>
```

## Installation & Import

```typescript
import { FinInputModule } from '@fincloud/ui/input';

// Or import individual components
import { FinInputComponent, FinInputCountdownComponent, FinInputProgressComponent } from '@fincloud/ui/input';
```

## Input Types & Masking

The library supports various input types with automatic formatting:

- `text` - Standard text input
- `password` - Password input with visibility toggle
- `number` - Numeric input
- `currency` - Currency formatting with locale support
- `percentage` - Percentage formatting
- `integer` - Integer-only input
- `months` - Month input formatting
- `decimal` - Decimal number formatting

## Advanced Features

- **AI Suggestions**: Enable AI-powered input suggestions with `aiEnabled` property
- **Localization**: Support for multiple locales (EN, DE) with appropriate formatting
- **Accessibility**: Full accessibility support with proper ARIA attributes
- **Reactive Forms**: Complete integration with Angular Reactive Forms
- **Validation**: Built-in validation message display and error handling
