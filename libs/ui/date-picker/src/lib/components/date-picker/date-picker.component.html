<label
  class="fin-label fin-flex fin-justify-between fin-items-center"
  [ngClass]="{
    'fin-text-color-text-disabled': control.disabled,
    'fin-text-color-text-primary': readonly,
  }"
  [for]="control"
>
  @if (label) {
    <span class="fin-w-full" finTruncateText> {{ label }} </span>
  } @else {
    <ng-content select="[finFieldLabel]"></ng-content>
  }
</label>
<mat-form-field
  class="fin-field fin-block fin-field-size-{{ size }} "
  cdkOverlayOrigin
  #datePickerTrigger="cdkOverlayOrigin"
  [subscriptSizing]="dynamicErrorSpace"
  [ngClass]="{
    'fin-field-warning':
      (externalFieldMessages?.type || (getMessage$ | async)?.type) ===
      'warning',
    'fin-field-success':
      (externalFieldMessages?.type || (getMessage$ | async)?.type) ===
      'success',
    'fin-field-readonly': readonly,
  }"
>
  @if (aiEnabled) {
    <fin-ai-suggestion
      [aiEnabled]="aiEnabled"
      [formControl]="control"
      (aiSuggestionReady)="onAiSuggestionReady()"
    >
    </fin-ai-suggestion>
  }
  <input
    matInput
    [formControl]="internalDateField"
    [placeholder]="readonly ? '' : placeholder"
    (blur)="onBlur($event)"
    [readonly]="readonly"
    [mask]="mask"
  />

  <div
    matSuffix
    class="fin-suffix fin-flex fin-items-center fin-justify-center"
  >
    @if (showIcon && !readonly) {
      <button
        type="button"
        [size]="sizes.M"
        [disabled]="control.disabled"
        fin-button-action
        (click)="openDatePicker($event)"
      >
        <fin-icon
          [class.fin-text-color-icons-primary]="control.enabled"
          name="date_range"
        >
        </fin-icon>
      </button>
    }

    <div class="fin-flex fin-items-center" (click)="$event.stopPropagation()">
      <ng-content select="[finFieldSuffix]"></ng-content>
    </div>
  </div>

  <mat-hint class="fin-w-full">
    @if (
      control.valid && control.touched && (getMessage$ | async);
      as message
    ) {
      @if (message.type === 'success') {
        <ng-container *ngTemplateOutlet="message.template"></ng-container>
      }
    } @else {
      <div class="fin-hint">
        <ng-content select="[finFieldHint]"></ng-content>
      </div>
    }
  </mat-hint>

  @if ((getMessage$ | async)?.template; as messageTemplate) {
    <mat-error>
      <div class="fin-inline-block">
        <ng-container *ngTemplateOutlet="messageTemplate"></ng-container>
      </div>
    </mat-error>
  }
</mat-form-field>

<!-- This template displays the overlay content and is connected to the input -->
<ng-template
  cdkConnectedOverlay
  [cdkConnectedOverlayOrigin]="datePickerTrigger"
  [cdkConnectedOverlayOpen]="isOpen"
  [cdkConnectedOverlayHasBackdrop]="true"
  (backdropClick)="isOpen = false"
  cdkConnectedOverlayBackdropClass="cdk-overlay-transparent-backdrop"
  [cdkConnectedOverlayPositions]="defaultPositionList"
  cdkConnectedOverlayPanelClass="fin-date-picker-overlay"
>
  <p-calendar
    #pCalendar
    [formControl]="internalDatepicker"
    [inline]="true"
    [placeholder]="placeholder"
    [view]="view"
    [selectionMode]="selectionMode"
    [showButtonBar]="showButtonBar"
    [minDate]="minDateParsed"
    [maxDate]="maxDateParsed"
    [disabledDates]="disabledDates"
    (onSelect)="onSelectDate()"
    [stepYearPicker]="16"
    [dateFormat]="defaultValueFormat"
  >
    @if (footerTemplate) {
      <ng-template pTemplate="footer">
        <div
          class="fin-flex fin-px-[1.2rem] fin-py-[0.4rem] fin-gap-[0.8rem]"
          (click)="closeDatePicker()"
        >
          <ng-container [ngTemplateOutlet]="footerTemplate"></ng-container>
        </div>
      </ng-template>
    }
  </p-calendar>
</ng-template>
