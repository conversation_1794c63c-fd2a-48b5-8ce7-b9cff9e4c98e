<div class="fin-m-[100px] fin-w-[900px]">
  <div>
    {{ word$ | async }}
  </div>
  <fin-input
    [formControl]="formControl"
    [label]="'Input label'"
    [aiEnabled]="aiEnabled"
    [size]="finSize.L"
  >
  </fin-input>

  <button type="button" (click)="aiEnabled = !aiEnabled">enable</button>
  <br />
  <br />
  <button
    type="button"
    (click)="
      formControl.setValue(
        'Lorem ipsum dolor sit amet consectetur adipisicing elit. Voluptate magni impedit delectus molestiae assumenda doloribus optio totam facilis aliquid explicabo ullam, provident accusamus nobis nostrum dolor, harum eligendi iusto consectetur.'
      )
    "
  >
    enable 2
  </button>

  <br />
  <br />
  <fin-text-area
    [formControl]="formControl"
    placeholder="Placeholder"
    label="Label"
    [aiEnabled]="aiEnabled"
  >
  </fin-text-area>
  <br />
  <br />
  <!-- <pre>
    loading    {{ loading | json }}
    aiPrediction    {{ aiPrediction | json }}
    rootFolderId    {{ rootFolderId | json }}
    readonly    {{ readonly | json }}
                    </pre
  > -->
  <fin-document-classification
    [formControl]="formControl"
    [loading]="loading"
    [rootFolderId]="rootFolderId"
    [aiPrediction]="aiPrediction"
    [readonly]="readonly"
  >
    <ng-template #finContent>Content</ng-template>
    <ng-template #finInputPrefix>pre</ng-template>
  </fin-document-classification>
</div>
<pre>{{ formControl.value | json }}</pre>
<button type="button" class="fin-bg-slate-400" (click)="stop()">button</button>
<br />
<br />
<!-- <button
  type="button"
  (click)="
    formControl.setValue('aaaaaaa');
    aiPrediction = true;
    rootFolderId = 'asd555';
    loading = false;
    disabled = true;
    readonly = true
  "
>
  button2
</button> -->
<br />
<br />
<button type="button" (click)="rootFolderId = 'aasdasdx'; aiPrediction = true">
  AI root id
</button>
<br />
<br />
<button type="button" (click)="rootFolderId = 'aasdasdx2'; aiPrediction = true">
  AI root id 2
</button>
<br />
<br />
<button
  type="button"
  (click)="rootFolderId = 'aasdasdx2111'; aiPrediction = false"
>
  manual
</button>
<br />
<br />
<button type="button" (click)="disabled = false">disabled</button>
